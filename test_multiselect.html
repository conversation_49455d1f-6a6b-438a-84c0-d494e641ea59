<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选框测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/css/material_porosity_flow_resistance_query.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>材料孔隙率流阻查询 - 搜索框测试</h2>
        
        <!-- 查询条件卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>查询条件
                </h5>
            </div>
            <div class="card-body">
                <form id="query-form">
                    <div class="row g-3 align-items-start">
                        <!-- 零件选择 -->
                        <div class="col-md-3">
                            <select class="form-select" id="part-select">
                                <option value="">请选择零件</option>
                                <option value="零件1">零件1</option>
                                <option value="零件2">零件2</option>
                                <option value="零件3">零件3</option>
                            </select>
                        </div>

                        <!-- 材料选择 -->
                        <div class="col-md-5">
                            <div class="material-multiselect" id="material-multiselect">
                                <div class="multiselect-container">
                                    <div class="multiselect-input-container">
                                        <input type="text" class="form-control multiselect-input" placeholder="请先选择零件..." readonly>
                                        <i class="fas fa-chevron-down multiselect-arrow"></i>
                                    </div>
                                    <div class="multiselect-dropdown">
                                        <div class="multiselect-search">
                                            <input type="text" class="form-control form-control-sm" placeholder="搜索材料...">
                                        </div>
                                        <div class="multiselect-options">
                                            <div class="multiselect-option" data-value="材料1">
                                                <input type="checkbox" value="材料1">
                                                <span>材料1</span>
                                            </div>
                                            <div class="multiselect-option" data-value="材料2">
                                                <input type="checkbox" value="材料2">
                                                <span>材料2</span>
                                            </div>
                                            <div class="multiselect-option" data-value="材料3">
                                                <input type="checkbox" value="材料3">
                                                <span>材料3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="selected-items mt-2">
                                    <!-- 已选择的材料标签 -->
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="col-md-4 d-flex align-items-start">
                            <div class="d-flex flex-column gap-2">
                                <button type="button" class="btn btn-primary" id="query-btn" disabled>
                                    <i class="fas fa-search me-1"></i>查询
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="reset-btn">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="button" class="btn btn-outline-success" id="export-btn" disabled>
                                    <i class="fas fa-download me-1"></i>导出
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 简单的多选框交互测试
            const $container = $('#material-multiselect');
            const $input = $container.find('.multiselect-input');
            const selectedItems = new Set();

            // 点击输入框显示/隐藏下拉框
            $container.find('.multiselect-input-container').on('click', function(e) {
                e.stopPropagation();
                if (!$input.prop('disabled')) {
                    $container.toggleClass('open');
                }
            });

            // 点击其他地方关闭下拉框
            $(document).on('click', function(e) {
                if (!$container.is(e.target) && $container.has(e.target).length === 0) {
                    $container.removeClass('open');
                }
            });

            // 材料选项点击事件
            $(document).on('click', '.multiselect-option', function(e) {
                e.stopPropagation();
                const checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked'));
                updateSelection(checkbox[0]);
            });

            // 复选框变化事件
            $(document).on('change', '.multiselect-option input[type="checkbox"]', function(e) {
                e.stopPropagation();
                updateSelection(this);
            });

            function updateSelection(checkbox) {
                const materialName = checkbox.value;
                const isChecked = checkbox.checked;
                const $option = $(checkbox).closest('.multiselect-option');

                if (isChecked) {
                    selectedItems.add(materialName);
                    $option.addClass('selected');
                } else {
                    selectedItems.delete(materialName);
                    $option.removeClass('selected');
                }

                updateDisplay();
            }

            function updateDisplay() {
                const $selectedItemsContainer = $('#material-multiselect .selected-items');
                
                // 更新输入框显示
                if (selectedItems.size === 0) {
                    $input.val('');
                } else if (selectedItems.size === 1) {
                    $input.val(Array.from(selectedItems)[0]);
                } else {
                    $input.val(`已选择 ${selectedItems.size} 个材料`);
                }
                
                // 更新选中项标签
                $selectedItemsContainer.empty();
                selectedItems.forEach(materialName => {
                    const tagHtml = `
                        <span class="selected-item" data-value="${materialName}">
                            ${materialName}
                            <span class="remove-btn">×</span>
                        </span>
                    `;
                    $selectedItemsContainer.append(tagHtml);
                });
            }

            // 移除标签事件
            $(document).on('click', '.selected-item .remove-btn', function(e) {
                e.stopPropagation();
                const materialName = $(this).closest('.selected-item').data('value');
                selectedItems.delete(materialName);
                
                // 更新选项状态
                const $checkbox = $(`.multiselect-option[data-value="${materialName}"] input[type="checkbox"]`);
                $checkbox.prop('checked', false);
                $checkbox.closest('.multiselect-option').removeClass('selected');
                
                updateDisplay();
            });

            // 零件选择变化事件
            $('#part-select').on('change', function() {
                const selectedPart = $(this).val();
                if (selectedPart) {
                    $input.prop('disabled', false);
                    $input.attr('placeholder', '请选择材料...');
                } else {
                    $input.prop('disabled', true);
                    $input.attr('placeholder', '请先选择零件...');
                    selectedItems.clear();
                    updateDisplay();
                    $container.removeClass('open');
                }
            });
        });
    </script>
</body>
</html>
