/* 材料孔隙率流阻查询模块样式 */

/* 表格样式优化 */
#data-table {
    white-space: nowrap; /* 数据行不换行 */
    table-layout: fixed;
    width: 100%;
}

#data-table thead th {
    white-space: normal; /* 表头允许换行 */
    word-wrap: break-word;
    vertical-align: middle;
    text-align: center;
    min-width: 80px;
    background-color: #343a40 !important;
    color: white;
    font-weight: 600;
}

#data-table tbody td {
    white-space: nowrap; /* 数据单元格不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    vertical-align: middle;
    padding: 8px;
}

#data-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* 材料多选框样式 */
.material-multiselect {
    position: relative;
    width: 100%;
}

.multiselect-container {
    position: relative;
}

.multiselect-input-container {
    position: relative;
    cursor: pointer;
    min-height: 38px;
}

.multiselect-input {
    cursor: pointer;
    background-color: white;
    padding-right: 40px;
}

.multiselect-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.multiselect-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    transition: transform 0.2s ease;
}

.multiselect-container.open .multiselect-arrow {
    transform: translateY(-50%) rotate(180deg);
}

.multiselect-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white !important;
    border: 2px solid #007bff !important;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3) !important;
    z-index: 9999 !important;
    max-height: 200px;
    overflow-y: auto;
    display: none !important;
}

.multiselect-container.open .multiselect-dropdown {
    display: block !important;
}

.multiselect-search {
    padding: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.multiselect-options {
    max-height: 150px;
    overflow-y: auto;
}

.multiselect-option {
    padding: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.15s ease;
}

.multiselect-option:hover {
    background-color: #f8f9fa;
}

.multiselect-option.selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

.multiselect-option input[type="checkbox"] {
    margin-right: 8px;
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    min-height: 32px;
    margin-top: 8px !important;
}

.selected-item {
    display: inline-flex;
    align-items: center;
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.selected-item .remove-btn {
    margin-left: 6px;
    cursor: pointer;
    color: #1976d2;
    font-weight: bold;
}

.selected-item .remove-btn:hover {
    color: #d32f2f;
}

/* 卡片样式 */
.card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* 表格响应式容器 */
.table-responsive {
    border-radius: 0.375rem;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* 徽章样式 */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* 空状态样式 */
#empty-state .fa-search {
    color: #6c757d;
}

/* 加载指示器样式 */
#loading-indicator {
    background-color: rgba(248, 249, 250, 0.8);
    border-radius: 0.375rem;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 错误提示样式 */
.alert {
    border-radius: 0.375rem;
    padding: 1rem;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .multiselect-dropdown {
        position: fixed;
        top: auto;
        left: 1rem;
        right: 1rem;
        max-height: 50vh;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .card-body {
        padding: 1rem;
    }

    #data-table thead th {
        font-size: 0.875rem;
        padding: 0.5rem 0.25rem;
    }

    #data-table tbody td {
        font-size: 0.875rem;
        padding: 0.5rem 0.25rem;
    }
}

@media (max-width: 576px) {
    .col-md-3,
    .col-md-5,
    .col-md-4 {
        margin-bottom: 1rem;
    }

    .d-flex.flex-column .btn {
        margin-bottom: 0.5rem;
    }

    .gap-2 {
        gap: 0.5rem !important;
    }
}
