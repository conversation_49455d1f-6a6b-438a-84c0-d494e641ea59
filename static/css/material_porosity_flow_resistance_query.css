/* 材料孔隙率流阻查询模块样式 */

/* 表格样式优化 */
#data-table {
    white-space: nowrap; /* 数据行不换行 */
    table-layout: fixed;
    width: 100%;
}

#data-table thead th {
    white-space: normal; /* 表头允许换行 */
    word-wrap: break-word;
    vertical-align: middle;
    text-align: center;
    min-width: 80px;
    background-color: #343a40 !important;
    color: white;
    font-weight: 600;
}

#data-table tbody td {
    white-space: nowrap; /* 数据单元格不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    vertical-align: middle;
    padding: 8px;
}

#data-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* 材料多选框样式 */
.material-checkbox-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: #fff;
}

.material-checkbox-item {
    display: block;
    padding: 0.25rem 0;
    cursor: pointer;
    transition: background-color 0.15s ease;
}

.material-checkbox-item:hover {
    background-color: #f8f9fa;
}

.material-checkbox-item input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* 卡片样式 */
.card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* 表格响应式容器 */
.table-responsive {
    border-radius: 0.375rem;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* 徽章样式 */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* 空状态样式 */
#empty-state .fa-search {
    color: #6c757d;
}

/* 加载指示器样式 */
#loading-indicator {
    background-color: rgba(248, 249, 250, 0.8);
    border-radius: 0.375rem;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 错误提示样式 */
.alert {
    border-radius: 0.375rem;
    padding: 1rem;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .material-checkbox-container {
        max-height: 150px;
    }
    
    #data-table thead th {
        font-size: 0.875rem;
        padding: 0.5rem 0.25rem;
    }
    
    #data-table tbody td {
        font-size: 0.875rem;
        padding: 0.5rem 0.25rem;
    }
}

@media (max-width: 576px) {
    .col-md-4,
    .col-md-6,
    .col-md-2 {
        margin-bottom: 1rem;
    }
    
    .d-flex.flex-column .btn {
        margin-bottom: 0.5rem;
    }
}
