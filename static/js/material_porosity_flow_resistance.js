/**
 * 材料孔隙率流阻查询功能
 */

class MaterialPorosityFlowResistanceQuery {
    constructor() {
        this.currentData = null;
        this.selectedMaterials = new Set(); // 存储选中的材料
        this.materials = []; // 存储所有材料数据
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadParts();
        this.initMaterialMultiselect();
    }

    bindEvents() {
        // 零件选择变化事件
        $('#part-select').on('change', () => this.onPartChange());

        // 查询按钮点击事件
        $('#query-btn').on('click', () => this.queryData());

        // 重置按钮点击事件
        $('#reset-btn').on('click', () => this.resetForm());

        // 导出按钮点击事件
        $('#export-btn').on('click', () => this.exportData());

        // 材料多选框事件
        this.bindMaterialMultiselectEvents();
    }

    initMaterialMultiselect() {
        const $container = $('#material-multiselect');
        const $input = $container.find('.multiselect-input');
        const $dropdown = $container.find('.multiselect-dropdown');
        const $search = $container.find('.multiselect-search input');

        console.log('initMaterialMultiselect - container found:', $container.length);
        console.log('initMaterialMultiselect - input found:', $input.length);
        console.log('initMaterialMultiselect - dropdown found:', $dropdown.length);

        // 点击输入框显示/隐藏下拉框
        $container.find('.multiselect-input-container').on('click', (e) => {
            console.log('Input container clicked, disabled:', $input.prop('disabled'));
            e.stopPropagation();
            if (!$input.prop('disabled')) {
                const wasOpen = $container.hasClass('open');
                $container.toggleClass('open');

                // 直接控制下拉框显示/隐藏，绕过CSS
                if (wasOpen) {
                    $dropdown.hide();
                    console.log('Hiding dropdown directly');
                } else {
                    $dropdown.show();
                    console.log('Showing dropdown directly');
                }

                console.log('Toggled dropdown, now open:', !wasOpen);
                console.log('Dropdown visibility:', $dropdown.is(':visible'));
            }
        });

        // 点击其他地方关闭下拉框
        $(document).on('click', (e) => {
            if (!$container.is(e.target) && $container.has(e.target).length === 0) {
                if ($container.hasClass('open')) {
                    console.log('Closing dropdown due to outside click');
                    $container.removeClass('open');
                }
            }
        });

        // 搜索功能
        $search.on('input', (e) => {
            console.log('Search input:', e.target.value);
            this.filterMaterialOptions(e.target.value);
        });
    }

    bindMaterialMultiselectEvents() {
        // 材料选项点击事件（委托事件）
        $(document).on('click', '.multiselect-option', (e) => {
            e.stopPropagation();
            const checkbox = $(e.currentTarget).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked'));
            this.onMaterialSelectionChange(checkbox[0]);
        });

        // 材料复选框变化事件（委托事件）
        $(document).on('change', '.multiselect-option input[type="checkbox"]', (e) => {
            e.stopPropagation();
            this.onMaterialSelectionChange(e.target);
        });

        // 移除已选材料标签事件（委托事件）
        $(document).on('click', '.selected-item .remove-btn', (e) => {
            e.stopPropagation();
            const materialName = $(e.target).closest('.selected-item').data('value');
            this.removeMaterial(materialName);
        });
    }

    async loadParts() {
        try {
            const response = await fetch('/material_porosity_flow_resistance/api/parts');
            const result = await response.json();
            
            if (result.code === 200) {
                this.populatePartSelect(result.data);
            } else {
                this.showError('加载零件列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载零件列表失败: ' + error.message);
        }
    }

    populatePartSelect(parts) {
        const $select = $('#part-select');
        $select.empty();
        $select.append('<option value="">请选择零件</option>');

        if (parts.length === 0) {
            $select.append('<option value="" disabled>暂无零件数据</option>');
            return;
        }

        parts.forEach(part => {
            $select.append(`<option value="${part.name}">${part.name}</option>`);
        });
    }

    async onPartChange() {
        this.selectedMaterials.clear(); // 清空已选材料
        this.updateMaterialMultiselectDisplay();
        this.loadMaterials();
        this.updateButtonStates();
    }

    async loadMaterials() {
        const selectedPart = $('#part-select').val();
        const $input = $('#material-multiselect .multiselect-input');

        console.log('loadMaterials called, selectedPart:', selectedPart);

        if (!selectedPart) {
            $input.attr('placeholder', '请先选择零件...');
            $input.prop('disabled', true);
            this.materials = [];
            this.populateMaterialOptions([]);
            return;
        }

        $input.prop('disabled', false);
        $input.attr('placeholder', '请选择材料...');

        try {
            let url = '/material_porosity_flow_resistance/api/materials';
            if (selectedPart) {
                url += `?part_names=${encodeURIComponent(selectedPart)}`;
            }

            console.log('Fetching materials from:', url);
            const response = await fetch(url);
            const result = await response.json();
            console.log('Materials API response:', result);

            if (result.code === 200) {
                this.materials = result.data;
                console.log('Populating materials:', result.data);
                this.populateMaterialOptions(result.data);
            } else {
                this.showError('加载材料列表失败: ' + result.message);
            }
        } catch (error) {
            console.error('Error loading materials:', error);
            this.showError('加载材料列表失败: ' + error.message);
        }
    }

    populateMaterialOptions(materials) {
        console.log('populateMaterialOptions called with:', materials);
        const $options = $('#material-multiselect .multiselect-options');
        console.log('Options container found:', $options.length);
        $options.empty();

        if (materials.length === 0) {
            console.log('No materials, showing empty message');
            $options.html('<div class="text-muted text-center py-2">暂无材料数据</div>');
            return;
        }

        console.log('Adding', materials.length, 'materials to options');
        materials.forEach(material => {
            const isSelected = this.selectedMaterials.has(material.name);
            const optionHtml = `
                <div class="multiselect-option ${isSelected ? 'selected' : ''}" data-value="${material.name}">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} value="${material.name}">
                    <span>${material.name}</span>
                </div>
            `;
            console.log('Adding material option:', material.name);
            $options.append(optionHtml);
        });
        console.log('Final options HTML:', $options.html());
    }

    filterMaterialOptions(searchText) {
        const $options = $('#material-multiselect .multiselect-options .multiselect-option');

        $options.each(function() {
            const materialName = $(this).find('span').text().toLowerCase();
            const matches = materialName.includes(searchText.toLowerCase());
            $(this).toggle(matches);
        });
    }

    onMaterialSelectionChange(checkbox) {
        const materialName = checkbox.value;
        const isChecked = checkbox.checked;
        const $option = $(checkbox).closest('.multiselect-option');

        if (isChecked) {
            this.selectedMaterials.add(materialName);
            $option.addClass('selected');
        } else {
            this.selectedMaterials.delete(materialName);
            $option.removeClass('selected');
        }

        this.updateMaterialMultiselectDisplay();
        this.updateButtonStates();
    }

    removeMaterial(materialName) {
        this.selectedMaterials.delete(materialName);

        // 更新选项状态
        const $checkbox = $(`.multiselect-option[data-value="${materialName}"] input[type="checkbox"]`);
        $checkbox.prop('checked', false);
        $checkbox.closest('.multiselect-option').removeClass('selected');

        this.updateMaterialMultiselectDisplay();
        this.updateButtonStates();
    }

    updateMaterialMultiselectDisplay() {
        const $input = $('#material-multiselect .multiselect-input');
        const $selectedItems = $('#material-multiselect .selected-items');

        // 更新输入框显示
        if (this.selectedMaterials.size === 0) {
            $input.val('');
        } else if (this.selectedMaterials.size === 1) {
            $input.val(Array.from(this.selectedMaterials)[0]);
        } else {
            $input.val(`已选择 ${this.selectedMaterials.size} 个材料`);
        }

        // 更新选中项标签
        $selectedItems.empty();
        this.selectedMaterials.forEach(materialName => {
            const tagHtml = `
                <span class="selected-item" data-value="${materialName}">
                    ${materialName}
                    <span class="remove-btn">×</span>
                </span>
            `;
            $selectedItems.append(tagHtml);
        });
    }

    updateButtonStates() {
        const selectedPart = $('#part-select').val();
        const hasSelection = selectedPart && this.selectedMaterials.size > 0;

        $('#query-btn').prop('disabled', !hasSelection);
        $('#export-btn').prop('disabled', !this.currentData || this.currentData.length === 0);
    }

    async queryData() {
        const selectedPart = $('#part-select').val();
        const selectedMaterials = Array.from(this.selectedMaterials);

        if (!selectedPart || selectedMaterials.length === 0) {
            this.showError('请选择零件和至少一个材料');
            return;
        }

        this.showLoading();
        this.hideError();

        try {
            let url = '/material_porosity_flow_resistance/api/query_data?';
            const params = [];

            params.push(`part_names=${encodeURIComponent(selectedPart)}`);

            selectedMaterials.forEach(material => {
                params.push(`material_names=${encodeURIComponent(material)}`);
            });

            url += params.join('&');

            const response = await fetch(url);
            const result = await response.json();

            if (result.code === 200) {
                this.currentData = result.data;
                this.displayResults(result.data);
                this.updateButtonStates();
            } else {
                this.showError(result.message);
                this.hideResults();
            }
        } catch (error) {
            this.showError('查询失败: ' + error.message);
            this.hideResults();
        } finally {
            this.hideLoading();
        }
    }

    displayResults(data) {
        this.hideEmptyState();
        this.showResults();

        // 更新记录数量
        $('#result-count').text(`${data.length} 条记录`);

        // 生成表格内容
        const $tbody = $('#data-table tbody');
        $tbody.empty();

        if (data.length === 0) {
            $tbody.append(`
                <tr>
                    <td colspan="9" class="text-center text-muted">暂无数据</td>
                </tr>
            `);
            return;
        }

        data.forEach(item => {
            const row = `
                <tr>
                    <td>${item.part_name || '-'}</td>
                    <td>${item.material_name || '-'}</td>
                    <td>${item.thickness || '-'}</td>
                    <td>${item.weight || '-'}</td>
                    <td>${item.density || '-'}</td>
                    <td>${item.porosity || '-'}</td>
                    <td>${item.porosity_deviation || '-'}</td>
                    <td>${item.flow_resistance || '-'}</td>
                    <td>${item.flow_resistance_deviation || '-'}</td>
                </tr>
            `;
            $tbody.append(row);
        });
    }

    async exportData() {
        const selectedPart = $('#part-select').val();
        const selectedMaterials = Array.from(this.selectedMaterials);

        if (!selectedPart || selectedMaterials.length === 0) {
            this.showError('请选择零件和至少一个材料');
            return;
        }

        try {
            let url = '/material_porosity_flow_resistance/api/export_csv?';
            const params = [];

            params.push(`part_names=${encodeURIComponent(selectedPart)}`);

            selectedMaterials.forEach(material => {
                params.push(`material_names=${encodeURIComponent(material)}`);
            });

            url += params.join('&');

            window.open(url, '_blank');
        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    resetForm() {
        $('#part-select').val('');
        this.selectedMaterials.clear();
        this.materials = [];

        // 重置材料多选框
        const $input = $('#material-multiselect .multiselect-input');
        const $selectedItems = $('#material-multiselect .selected-items');
        const $options = $('#material-multiselect .multiselect-options');
        const $search = $('#material-multiselect .multiselect-search input');

        $input.val('');
        $input.attr('placeholder', '请先选择零件...');
        $input.prop('disabled', true);
        $selectedItems.empty();
        $options.empty();
        $search.val('');

        // 关闭下拉框
        $('#material-multiselect .multiselect-container').removeClass('open');

        this.currentData = null;
        this.hideResults();
        this.hideError();
        this.showEmptyState();
        this.updateButtonStates();
    }

    showLoading() {
        $('#loading-indicator').show();
    }

    hideLoading() {
        $('#loading-indicator').hide();
    }

    showResults() {
        $('#results-card').show();
    }

    hideResults() {
        $('#results-card').hide();
    }

    showEmptyState() {
        $('#empty-state').show();
    }

    hideEmptyState() {
        $('#empty-state').hide();
    }

    showError(message) {
        $('#error-message').text(message);
        $('#error-alert').show();
    }

    hideError() {
        $('#error-alert').hide();
    }
}
